// Main Application Script
class SearchEngine {
    constructor() {
        this.contentGenerator = new ContentGenerator();
        this.imageAnalyzer = new ImageAnalyzer();
        this.currentMode = 'text';
        this.currentQuery = '';
        this.currentImage = null;
        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        // Text search elements
        this.searchInput = document.getElementById('searchInput');
        this.searchButton = document.getElementById('searchButton');
        this.textSearchContainer = document.getElementById('textSearchContainer');
        this.searchSuggestions = document.getElementById('searchSuggestions');

        // Image upload elements
        this.textModeBtn = document.getElementById('textModeBtn');
        this.imageModeBtn = document.getElementById('imageModeBtn');
        this.imageUploadContainer = document.getElementById('imageUploadContainer');
        this.uploadArea = document.getElementById('uploadArea');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.imageInput = document.getElementById('imageInput');
        this.imagePreview = document.getElementById('imagePreview');
        this.previewImage = document.getElementById('previewImage');
        this.analyzeBtn = document.getElementById('analyzeBtn');
        this.removeBtn = document.getElementById('removeBtn');

        // Common elements
        this.loadingState = document.getElementById('loadingState');
        this.resultsContainer = document.getElementById('resultsContainer');
        this.blogContent = document.getElementById('blogContent');
        this.currentQueryDisplay = document.getElementById('currentQuery');
        this.wordCount = document.getElementById('wordCount');
        this.generationTime = document.getElementById('generationTime');
        this.newSearchBtn = document.getElementById('newSearchBtn');
    }

    bindEvents() {
        // Mode toggle functionality
        this.textModeBtn.addEventListener('click', () => this.switchMode('text'));
        this.imageModeBtn.addEventListener('click', () => this.switchMode('image'));

        // Text search functionality
        this.searchButton.addEventListener('click', () => this.performSearch());
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // Search suggestions
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        this.searchInput.addEventListener('focus', () => {
            this.showSuggestions();
        });

        document.addEventListener('click', (e) => {
            if (!this.textSearchContainer.contains(e.target)) {
                this.hideSuggestions();
            }
        });

        // Image upload functionality
        this.uploadBtn.addEventListener('click', () => this.imageInput.click());
        this.imageInput.addEventListener('change', (e) => this.handleImageSelect(e));
        this.analyzeBtn.addEventListener('click', () => this.analyzeImage());
        this.removeBtn.addEventListener('click', () => this.removeImage());

        // Drag and drop functionality
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        this.uploadArea.addEventListener('click', () => this.imageInput.click());

        // New search functionality
        this.newSearchBtn.addEventListener('click', () => {
            this.resetToSearch();
        });

        // Prevent form submission on enter
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
            }
        });
    }

    handleSearchInput(value) {
        if (value.length > 0) {
            this.updateSuggestions(value);
            this.showSuggestions();
        } else {
            this.hideSuggestions();
        }
    }

    updateSuggestions(query) {
        const suggestions = this.contentGenerator.getSuggestions(query);

        if (suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }

        this.searchSuggestions.innerHTML = '';

        suggestions.forEach(suggestion => {
            const suggestionElement = document.createElement('div');
            suggestionElement.className = 'suggestion-item';
            suggestionElement.textContent = suggestion;

            suggestionElement.addEventListener('click', () => {
                this.searchInput.value = suggestion;
                this.hideSuggestions();
                this.performSearch();
            });

            this.searchSuggestions.appendChild(suggestionElement);
        });
    }

    showSuggestions() {
        if (this.searchSuggestions.children.length > 0) {
            this.searchSuggestions.style.display = 'block';
        }
    }

    hideSuggestions() {
        this.searchSuggestions.style.display = 'none';
    }

    async performSearch() {
        const query = this.searchInput.value.trim();

        if (!query) {
            this.showError('Please enter a search query');
            return;
        }

        this.currentQuery = query;
        this.hideSuggestions();
        this.showLoading();

        try {
            // Simulate API delay for realistic experience
            await this.delay(1000 + Math.random() * 1500);

            const result = this.contentGenerator.generateContent(query);
            this.displayResults(result);
        } catch (error) {
            this.showError('An error occurred while generating content. Please try again.');
            console.error('Search error:', error);
        }
    }

    showLoading() {
        this.textSearchContainer.style.display = 'none';
        this.imageUploadContainer.style.display = 'none';
        this.resultsContainer.style.display = 'none';
        this.loadingState.style.display = 'block';
    }

    displayResults(result, queryText = null) {
        this.loadingState.style.display = 'none';
        this.textSearchContainer.style.display = 'none';
        this.imageUploadContainer.style.display = 'none';

        // Update content
        this.blogContent.innerHTML = result.html;
        this.currentQueryDisplay.textContent = queryText || this.currentQuery;
        this.wordCount.textContent = result.wordCount;
        this.generationTime.textContent = result.generationTime;

        // Show results with animation
        this.resultsContainer.style.display = 'block';

        // Scroll to top of results
        this.resultsContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // Mode switching functionality
    switchMode(mode) {
        this.currentMode = mode;

        // Update button states
        this.textModeBtn.classList.toggle('active', mode === 'text');
        this.imageModeBtn.classList.toggle('active', mode === 'image');

        // Show/hide containers
        this.textSearchContainer.style.display = mode === 'text' ? 'block' : 'none';
        this.imageUploadContainer.style.display = mode === 'image' ? 'block' : 'none';

        // Hide results and reset
        this.resultsContainer.style.display = 'none';
        this.loadingState.style.display = 'none';

        if (mode === 'image') {
            this.removeImage();
        }
    }

    // Image upload handling
    handleImageSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.processImageFile(file);
        }
    }

    handleDragOver(event) {
        event.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');

        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.processImageFile(files[0]);
        }
    }

    processImageFile(file) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showError('Please select a valid image file.');
            return;
        }

        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            this.showError('File size must be less than 10MB.');
            return;
        }

        this.currentImage = file;

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
            this.previewImage.src = e.target.result;
            this.uploadArea.style.display = 'none';
            this.imagePreview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }

    removeImage() {
        this.currentImage = null;
        this.imageInput.value = '';
        this.previewImage.src = '';
        this.uploadArea.style.display = 'block';
        this.imagePreview.style.display = 'none';
    }

    async analyzeImage() {
        if (!this.currentImage) {
            this.showError('Please select an image first.');
            return;
        }

        this.showLoading();

        try {
            // Wait for image to load to get dimensions
            await this.waitForImageLoad();

            const result = await this.imageAnalyzer.analyzeImage(this.currentImage, this.previewImage);
            this.displayResults(result, this.currentImage.name);
        } catch (error) {
            this.showError('An error occurred while analyzing the image. Please try again.');
            console.error('Image analysis error:', error);
        }
    }

    waitForImageLoad() {
        return new Promise((resolve) => {
            if (this.previewImage.complete) {
                resolve();
            } else {
                this.previewImage.onload = resolve;
            }
        });
    }

    resetToSearch() {
        this.resultsContainer.style.display = 'none';
        this.loadingState.style.display = 'none';

        if (this.currentMode === 'text') {
            this.textSearchContainer.style.display = 'block';
            this.searchInput.value = '';
            this.searchInput.focus();
        } else {
            this.imageUploadContainer.style.display = 'block';
            this.removeImage();
        }

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    showError(message) {
        this.loadingState.style.display = 'none';

        // Create error display
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            background: #fee;
            color: #c33;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            border: 1px solid #fcc;
        `;
        errorDiv.textContent = message;

        // Insert after search container
        this.searchContainer.insertAdjacentElement('afterend', errorDiv);

        // Remove error after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const searchEngine = new SearchEngine();

    // Add some sample queries for demonstration
    const sampleQueries = [
        'mindfulness meditation',
        'climate change',
        'healthy cooking',
        'artificial intelligence',
        'sustainable living'
    ];

    // Optional: Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Focus search input with Ctrl+K or Cmd+K
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            searchEngine.searchInput.focus();
        }

        // Escape to return to search
        if (e.key === 'Escape') {
            if (searchEngine.resultsContainer.style.display === 'block') {
                searchEngine.resetToSearch();
            }
        }
    });

    console.log('KnowledgeSearch initialized successfully!');
    console.log('Try searching for:', sampleQueries.join(', '));
});

// Add some utility functions for enhanced user experience
function addSearchShortcuts() {
    // Add visual indicator for keyboard shortcuts
    const searchInput = document.getElementById('searchInput');
    const originalPlaceholder = searchInput.placeholder;

    // Detect if user is on Mac or PC for appropriate shortcut display
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    const shortcut = isMac ? '⌘K' : 'Ctrl+K';

    searchInput.placeholder = `${originalPlaceholder} (${shortcut} to focus)`;
}

// Initialize shortcuts when page loads
document.addEventListener('DOMContentLoaded', addSearchShortcuts);

// Add smooth scrolling for better UX
document.documentElement.style.scrollBehavior = 'smooth';

// Add loading animation enhancement
function enhanceLoadingAnimation() {
    const style = document.createElement('style');
    style.textContent = `
        .loading-spinner {
            background: conic-gradient(from 0deg, transparent, white, transparent);
        }
    `;
    document.head.appendChild(style);
}

document.addEventListener('DOMContentLoaded', enhanceLoadingAnimation);
