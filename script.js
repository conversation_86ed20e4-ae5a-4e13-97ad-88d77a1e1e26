// Main Application Script
class SearchEngine {
    constructor() {
        this.contentGenerator = new ContentGenerator();
        this.initializeElements();
        this.bindEvents();
        this.currentQuery = '';
    }

    initializeElements() {
        this.searchInput = document.getElementById('searchInput');
        this.searchButton = document.getElementById('searchButton');
        this.searchContainer = document.getElementById('searchContainer');
        this.searchSuggestions = document.getElementById('searchSuggestions');
        this.loadingState = document.getElementById('loadingState');
        this.resultsContainer = document.getElementById('resultsContainer');
        this.blogContent = document.getElementById('blogContent');
        this.currentQueryDisplay = document.getElementById('currentQuery');
        this.wordCount = document.getElementById('wordCount');
        this.generationTime = document.getElementById('generationTime');
        this.newSearchBtn = document.getElementById('newSearchBtn');
    }

    bindEvents() {
        // Search functionality
        this.searchButton.addEventListener('click', () => this.performSearch());
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // Search suggestions
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        this.searchInput.addEventListener('focus', () => {
            this.showSuggestions();
        });

        document.addEventListener('click', (e) => {
            if (!this.searchContainer.contains(e.target)) {
                this.hideSuggestions();
            }
        });

        // New search functionality
        this.newSearchBtn.addEventListener('click', () => {
            this.resetToSearch();
        });

        // Prevent form submission on enter
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
            }
        });
    }

    handleSearchInput(value) {
        if (value.length > 0) {
            this.updateSuggestions(value);
            this.showSuggestions();
        } else {
            this.hideSuggestions();
        }
    }

    updateSuggestions(query) {
        const suggestions = this.contentGenerator.getSuggestions(query);
        
        if (suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }

        this.searchSuggestions.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const suggestionElement = document.createElement('div');
            suggestionElement.className = 'suggestion-item';
            suggestionElement.textContent = suggestion;
            
            suggestionElement.addEventListener('click', () => {
                this.searchInput.value = suggestion;
                this.hideSuggestions();
                this.performSearch();
            });
            
            this.searchSuggestions.appendChild(suggestionElement);
        });
    }

    showSuggestions() {
        if (this.searchSuggestions.children.length > 0) {
            this.searchSuggestions.style.display = 'block';
        }
    }

    hideSuggestions() {
        this.searchSuggestions.style.display = 'none';
    }

    async performSearch() {
        const query = this.searchInput.value.trim();
        
        if (!query) {
            this.showError('Please enter a search query');
            return;
        }

        this.currentQuery = query;
        this.hideSuggestions();
        this.showLoading();

        try {
            // Simulate API delay for realistic experience
            await this.delay(1000 + Math.random() * 1500);
            
            const result = this.contentGenerator.generateContent(query);
            this.displayResults(result);
        } catch (error) {
            this.showError('An error occurred while generating content. Please try again.');
            console.error('Search error:', error);
        }
    }

    showLoading() {
        this.searchContainer.style.display = 'none';
        this.resultsContainer.style.display = 'none';
        this.loadingState.style.display = 'block';
    }

    displayResults(result) {
        this.loadingState.style.display = 'none';
        this.searchContainer.style.display = 'none';
        
        // Update content
        this.blogContent.innerHTML = result.html;
        this.currentQueryDisplay.textContent = this.currentQuery;
        this.wordCount.textContent = result.wordCount;
        this.generationTime.textContent = result.generationTime;
        
        // Show results with animation
        this.resultsContainer.style.display = 'block';
        
        // Scroll to top of results
        this.resultsContainer.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
        });
    }

    resetToSearch() {
        this.resultsContainer.style.display = 'none';
        this.loadingState.style.display = 'none';
        this.searchContainer.style.display = 'block';
        
        // Clear search input and focus
        this.searchInput.value = '';
        this.searchInput.focus();
        
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    showError(message) {
        this.loadingState.style.display = 'none';
        
        // Create error display
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            background: #fee;
            color: #c33;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            border: 1px solid #fcc;
        `;
        errorDiv.textContent = message;
        
        // Insert after search container
        this.searchContainer.insertAdjacentElement('afterend', errorDiv);
        
        // Remove error after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const searchEngine = new SearchEngine();
    
    // Add some sample queries for demonstration
    const sampleQueries = [
        'mindfulness meditation',
        'climate change',
        'healthy cooking',
        'artificial intelligence',
        'sustainable living'
    ];
    
    // Optional: Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Focus search input with Ctrl+K or Cmd+K
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            searchEngine.searchInput.focus();
        }
        
        // Escape to return to search
        if (e.key === 'Escape') {
            if (searchEngine.resultsContainer.style.display === 'block') {
                searchEngine.resetToSearch();
            }
        }
    });
    
    console.log('KnowledgeSearch initialized successfully!');
    console.log('Try searching for:', sampleQueries.join(', '));
});

// Add some utility functions for enhanced user experience
function addSearchShortcuts() {
    // Add visual indicator for keyboard shortcuts
    const searchInput = document.getElementById('searchInput');
    const originalPlaceholder = searchInput.placeholder;
    
    // Detect if user is on Mac or PC for appropriate shortcut display
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    const shortcut = isMac ? '⌘K' : 'Ctrl+K';
    
    searchInput.placeholder = `${originalPlaceholder} (${shortcut} to focus)`;
}

// Initialize shortcuts when page loads
document.addEventListener('DOMContentLoaded', addSearchShortcuts);

// Add smooth scrolling for better UX
document.documentElement.style.scrollBehavior = 'smooth';

// Add loading animation enhancement
function enhanceLoadingAnimation() {
    const style = document.createElement('style');
    style.textContent = `
        .loading-spinner {
            background: conic-gradient(from 0deg, transparent, white, transparent);
        }
    `;
    document.head.appendChild(style);
}

document.addEventListener('DOMContentLoaded', enhanceLoadingAnimation);
