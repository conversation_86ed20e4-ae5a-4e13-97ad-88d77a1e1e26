/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --modern-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    --accent-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: rgba(255, 255, 255, 0.9);
    --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.2);
    --border-radius: 16px;
    --border-radius-small: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--modern-gradient);
    min-height: 100vh;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(42, 82, 152, 0.4) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    text-align: center;
    padding: 60px 0 40px;
    color: var(--text-light);
    position: relative;
}

.logo h1 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    letter-spacing: -0.02em;
    position: relative;
}

.logo h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--accent-gradient);
    border-radius: 2px;
}

.tagline {
    font-size: 1.2rem;
    opacity: 0.85;
    font-weight: 400;
    letter-spacing: 0.5px;
    margin-top: 8px;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 0;
    position: relative;
}

/* Search Container */
.search-container {
    width: 100%;
    max-width: 700px;
    margin-bottom: 60px;
    position: relative;
}

.search-box {
    position: relative;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 60px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    transition: var(--transition);
}

.search-box:hover {
    box-shadow: var(--shadow-heavy);
    background: rgba(255, 255, 255, 0.15);
}

.search-box:focus-within {
    box-shadow: var(--shadow-heavy);
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

#searchInput {
    width: 100%;
    padding: 22px 70px 22px 28px;
    border: none;
    outline: none;
    font-size: 1.1rem;
    background: transparent;
    color: white;
    font-weight: 400;
}

#searchInput::placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 300;
}

.search-btn {
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--accent-gradient);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.search-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.search-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* Search Mode Toggle */
.search-mode-toggle {
    display: flex;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 50px;
    padding: 6px;
    margin-bottom: 32px;
    box-shadow: var(--shadow-light);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.mode-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 50px;
    background: transparent;
    color: var(--text-light);
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.mode-btn.active {
    background: var(--accent-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.mode-btn:not(.active):hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.mode-btn svg {
    transition: var(--transition);
}

.mode-btn.active svg {
    transform: scale(1.1);
}

/* Image Upload Container */
.image-upload-container {
    width: 100%;
    max-width: 700px;
    margin-bottom: 60px;
}

.upload-area {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 2px dashed var(--glass-border);
    border-radius: var(--border-radius);
    padding: 48px 32px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    border-color: rgba(255, 107, 107, 0.5);
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: var(--accent-gradient);
    background: rgba(255, 107, 107, 0.1);
    transform: scale(1.02);
}

.upload-content h3 {
    color: var(--text-light);
    font-size: 1.4rem;
    font-weight: 600;
    margin: 16px 0 8px;
}

.upload-content p {
    color: rgba(255, 255, 255, 0.8);
    margin: 8px 0;
    font-size: 1rem;
}

.upload-formats {
    font-size: 0.85rem !important;
    opacity: 0.7;
}

.upload-icon {
    color: var(--text-light);
    opacity: 0.8;
    margin-bottom: 8px;
}

.upload-btn {
    background: var(--accent-gradient);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 16px;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* Image Preview */
.image-preview {
    margin-top: 24px;
    animation: slideDown 0.3s ease-out;
}

.preview-container {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow-medium);
}

.preview-container img {
    width: 100%;
    max-width: 400px;
    max-height: 300px;
    object-fit: contain;
    border-radius: var(--border-radius-small);
    margin-bottom: 20px;
    box-shadow: var(--shadow-light);
}

.preview-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.analyze-btn, .remove-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 30px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
}

.analyze-btn {
    background: var(--accent-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.remove-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    border: 1px solid var(--glass-border);
}

.remove-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Search Suggestions */
.search-suggestions {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    margin-top: 12px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    display: none;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.suggestion-item {
    padding: 16px 24px;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    font-weight: 400;
    position: relative;
    overflow: hidden;
}

.suggestion-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: var(--accent-gradient);
    transition: width 0.3s ease;
    z-index: -1;
}

.suggestion-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.suggestion-item:hover::before {
    width: 4px;
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* Loading State */
.loading {
    text-align: center;
    color: var(--text-light);
    padding: 80px 20px;
    position: relative;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-top: 4px solid var(--accent-gradient);
    border-radius: 50%;
    animation: modernSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    margin: 0 auto 24px;
    position: relative;
}

.loading-spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 2px solid transparent;
    border-top: 2px solid rgba(255, 107, 107, 0.8);
    border-radius: 50%;
    animation: modernSpin 0.8s linear infinite reverse;
}

@keyframes modernSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 400;
    letter-spacing: 0.5px;
}

/* Results Container */
.results-container {
    width: 100%;
    max-width: 900px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: var(--shadow-heavy);
    overflow: hidden;
    animation: modernSlideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.results-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accent-gradient);
}

@keyframes modernSlideUp {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.results-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 24px 32px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    position: relative;
}

.new-search-btn {
    background: var(--accent-gradient);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 30px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    letter-spacing: 0.5px;
}

.new-search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.new-search-btn:active {
    transform: translateY(0);
}

.query-display {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.query-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.current-query {
    color: #1e3c72;
    font-weight: 700;
    background: linear-gradient(135deg, #e8f4fd 0%, #d6e9f7 100%);
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid rgba(30, 60, 114, 0.1);
    font-size: 0.9rem;
    letter-spacing: 0.3px;
}

/* Blog Content */
.blog-content {
    padding: 48px 40px;
    line-height: 1.8;
    position: relative;
}

.blog-content h1 {
    color: var(--text-primary);
    font-size: 2.5rem;
    margin-bottom: 24px;
    font-weight: 700;
    letter-spacing: -0.02em;
    line-height: 1.2;
    position: relative;
}

.blog-content h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 80px;
    height: 4px;
    background: var(--accent-gradient);
    border-radius: 2px;
}

.blog-content h2 {
    color: var(--text-primary);
    font-size: 1.6rem;
    margin: 40px 0 20px;
    font-weight: 700;
    position: relative;
    padding-left: 24px;
    letter-spacing: -0.01em;
}

.blog-content h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 24px;
    background: var(--accent-gradient);
    border-radius: 3px;
}

.blog-content h3 {
    color: var(--text-primary);
    font-size: 1.3rem;
    margin: 32px 0 16px;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.blog-content p {
    margin-bottom: 20px;
    color: #4a5568;
    text-align: justify;
    font-size: 1.05rem;
    line-height: 1.7;
}

.blog-content ul, .blog-content ol {
    margin: 20px 0 20px 32px;
}

.blog-content li {
    margin-bottom: 12px;
    color: #4a5568;
    font-size: 1.05rem;
    line-height: 1.6;
}

.blog-content strong {
    color: var(--text-primary);
    font-weight: 700;
}

.blog-content em {
    color: #1e3c72;
    font-style: italic;
    font-weight: 500;
}

.intro-section {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 28px;
    border-radius: var(--border-radius);
    margin-bottom: 32px;
    border: 1px solid rgba(30, 60, 114, 0.1);
    position: relative;
    overflow: hidden;
}

.intro-section::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    background: var(--accent-gradient);
}

.conclusion-section {
    background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
    padding: 28px;
    border-radius: var(--border-radius);
    margin-top: 32px;
    border: 1px solid rgba(72, 187, 120, 0.2);
    position: relative;
    overflow: hidden;
}

.conclusion-section::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

/* Content Footer */
.content-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 32px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-secondary);
    position: relative;
}

.content-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--accent-gradient);
    opacity: 0.3;
}

.word-count, .generation-time {
    font-weight: 600;
    color: var(--text-primary);
}

/* Footer */
.footer {
    text-align: center;
    padding: 40px 0;
    color: var(--text-light);
    font-size: 0.95rem;
    font-weight: 400;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }

    .header {
        padding: 40px 0 30px;
    }

    .logo h1 {
        font-size: 3rem;
    }

    .search-container {
        max-width: 100%;
        margin-bottom: 40px;
    }

    #searchInput {
        font-size: 1rem;
        padding: 18px 60px 18px 24px;
    }

    .search-btn {
        width: 46px;
        height: 46px;
    }

    .results-container {
        max-width: 100%;
        border-radius: 20px;
    }

    .results-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        padding: 20px 24px;
        gap: 12px;
    }

    .blog-content {
        padding: 32px 24px;
    }

    .blog-content h1 {
        font-size: 2rem;
    }

    .blog-content h2 {
        font-size: 1.4rem;
        margin: 32px 0 16px;
    }

    .content-footer {
        flex-direction: column;
        gap: 12px;
        text-align: center;
        padding: 16px 24px;
    }

    .intro-section, .conclusion-section {
        padding: 20px;
        margin: 24px 0;
    }

    .search-mode-toggle {
        max-width: 100%;
        margin-bottom: 24px;
    }

    .mode-btn {
        padding: 10px 16px;
        font-size: 0.85rem;
    }

    .upload-area {
        padding: 32px 20px;
    }

    .upload-content h3 {
        font-size: 1.2rem;
    }

    .preview-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .analyze-btn, .remove-btn {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 30px 0 20px;
    }

    .logo h1 {
        font-size: 2.5rem;
    }

    .tagline {
        font-size: 1rem;
    }

    .search-container {
        margin-bottom: 30px;
    }

    #searchInput {
        padding: 16px 55px 16px 20px;
        font-size: 0.95rem;
    }

    .search-btn {
        width: 42px;
        height: 42px;
        right: 4px;
    }

    .results-container {
        border-radius: 16px;
    }

    .blog-content {
        padding: 24px 20px;
    }

    .blog-content h1 {
        font-size: 1.8rem;
    }

    .blog-content h2 {
        font-size: 1.3rem;
        padding-left: 20px;
    }

    .blog-content h2::before {
        width: 4px;
        height: 20px;
    }

    .blog-content h3 {
        font-size: 1.2rem;
    }

    .blog-content p, .blog-content li {
        font-size: 1rem;
    }

    .intro-section, .conclusion-section {
        padding: 16px;
        margin: 20px 0;
    }

    .search-mode-toggle {
        padding: 4px;
        margin-bottom: 20px;
    }

    .mode-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
        gap: 6px;
    }

    .upload-area {
        padding: 24px 16px;
    }

    .upload-content h3 {
        font-size: 1.1rem;
    }

    .upload-content p {
        font-size: 0.9rem;
    }

    .upload-formats {
        font-size: 0.8rem !important;
    }

    .preview-container {
        padding: 16px;
    }
}

/* Modern scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
}

/* Selection styling */
::selection {
    background: rgba(255, 107, 107, 0.3);
    color: var(--text-primary);
}

::-moz-selection {
    background: rgba(255, 107, 107, 0.3);
    color: var(--text-primary);
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible {
    outline: 2px solid var(--accent-gradient);
    outline-offset: 2px;
}

/* Smooth animations for reduced motion users */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
