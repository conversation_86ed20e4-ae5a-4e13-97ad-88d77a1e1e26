/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    text-align: center;
    padding: 40px 0 20px;
    color: white;
}

.logo h1 {
    font-size: 3.5rem;
    font-weight: 300;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.tagline {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 0;
}

/* Search Container */
.search-container {
    width: 100%;
    max-width: 600px;
    margin-bottom: 40px;
}

.search-box {
    position: relative;
    background: white;
    border-radius: 50px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-box:hover {
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.search-box:focus-within {
    box-shadow: 0 12px 40px rgba(0,0,0,0.2);
    transform: translateY(-2px);
}

#searchInput {
    width: 100%;
    padding: 18px 60px 18px 24px;
    border: none;
    outline: none;
    font-size: 1.1rem;
    background: transparent;
}

#searchInput::placeholder {
    color: #999;
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: #667eea;
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
}

.search-btn:hover {
    background: #5a6fd8;
    transform: translateY(-50%) scale(1.05);
}

/* Search Suggestions */
.search-suggestions {
    background: white;
    border-radius: 12px;
    margin-top: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    display: none;
}

.suggestion-item {
    padding: 12px 20px;
    cursor: pointer;
    transition: background 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.suggestion-item:hover {
    background: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* Loading State */
.loading {
    text-align: center;
    color: white;
    padding: 60px 20px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255,255,255,0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Results Container */
.results-container {
    width: 100%;
    max-width: 800px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 12px 48px rgba(0,0,0,0.1);
    overflow: hidden;
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.results-header {
    background: #f8f9fa;
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.new-search-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.new-search-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.query-display {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.query-label {
    color: #666;
    font-weight: 500;
}

.current-query {
    color: #667eea;
    font-weight: 600;
    background: #e8ecff;
    padding: 4px 12px;
    border-radius: 12px;
}

/* Blog Content */
.blog-content {
    padding: 40px 30px;
    line-height: 1.8;
}

.blog-content h1 {
    color: #2c3e50;
    font-size: 2.2rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.blog-content h2 {
    color: #34495e;
    font-size: 1.5rem;
    margin: 30px 0 15px;
    font-weight: 600;
    border-left: 4px solid #667eea;
    padding-left: 15px;
}

.blog-content h3 {
    color: #34495e;
    font-size: 1.2rem;
    margin: 25px 0 12px;
    font-weight: 600;
}

.blog-content p {
    margin-bottom: 16px;
    color: #444;
    text-align: justify;
}

.blog-content ul, .blog-content ol {
    margin: 16px 0 16px 30px;
}

.blog-content li {
    margin-bottom: 8px;
    color: #444;
}

.blog-content strong {
    color: #2c3e50;
    font-weight: 600;
}

.blog-content em {
    color: #667eea;
    font-style: italic;
}

.intro-section {
    background: #f8f9ff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #667eea;
}

.conclusion-section {
    background: #f0f8f0;
    padding: 20px;
    border-radius: 8px;
    margin-top: 25px;
    border-left: 4px solid #28a745;
}

/* Content Footer */
.content-footer {
    background: #f8f9fa;
    padding: 15px 30px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #666;
}

/* Footer */
.footer {
    text-align: center;
    padding: 30px 0;
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .logo h1 {
        font-size: 2.5rem;
    }
    
    .search-container {
        max-width: 100%;
    }
    
    #searchInput {
        font-size: 1rem;
        padding: 16px 55px 16px 20px;
    }
    
    .results-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .blog-content {
        padding: 30px 20px;
    }
    
    .blog-content h1 {
        font-size: 1.8rem;
    }
    
    .content-footer {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .logo h1 {
        font-size: 2rem;
    }
    
    .blog-content {
        padding: 25px 15px;
    }
    
    .blog-content h1 {
        font-size: 1.6rem;
    }
    
    .blog-content h2 {
        font-size: 1.3rem;
    }
}
