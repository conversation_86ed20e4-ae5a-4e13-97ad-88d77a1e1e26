// Content Generator Module
class ContentGenerator {
    constructor() {
        this.contentTemplates = {
            // Health & Wellness
            'mindfulness meditation': {
                title: 'Mindfulness Meditation: A Complete Guide to Present-Moment Awareness',
                sections: [
                    {
                        heading: 'Understanding Mindfulness Meditation',
                        content: `Mindfulness meditation is a mental training practice that teaches you to slow down racing thoughts, let go of negativity, and calm both your mind and body. It combines meditation with the practice of mindfulness, which can be defined as a mental state that involves being fully focused on "the now" so you can acknowledge and accept your thoughts, feelings, and sensations without judgment.

The practice has its roots in Buddhist teachings, but its secular applications have made it popular in Western psychology and wellness practices. Unlike other forms of meditation that focus on emptying the mind, mindfulness meditation encourages practitioners to pay attention to thoughts and feelings without getting caught up in them.`
                    },
                    {
                        heading: 'Scientific Benefits of Mindfulness Meditation',
                        content: `Research has consistently shown that regular mindfulness meditation practice offers numerous mental and physical health benefits. Studies indicate that it can reduce stress, anxiety, and depression while improving focus, emotional regulation, and overall well-being.

Neuroimaging studies have revealed that mindfulness meditation can actually change brain structure. Regular practice increases gray matter density in areas associated with learning, memory, and emotional regulation, while decreasing activity in the amygdala, the brain's fear center. Additionally, it can lower blood pressure, improve immune function, and reduce chronic pain.`
                    },
                    {
                        heading: 'Step-by-Step Practice Guide',
                        content: `<strong>Getting Started:</strong>
1. Find a quiet, comfortable space where you won't be disturbed
2. Sit in a comfortable position with your back straight but not rigid
3. Close your eyes or soften your gaze downward
4. Begin by taking three deep, conscious breaths

<strong>The Practice:</strong>
1. Focus your attention on your breath as it flows in and out
2. Notice the sensation of breathing without trying to control it
3. When your mind wanders (and it will), gently return focus to your breath
4. Observe thoughts and feelings without judgment, letting them pass like clouds
5. Continue for 5-20 minutes, gradually increasing duration over time

<strong>Ending the Session:</strong>
Take a moment to notice how you feel, then slowly open your eyes and return to your day with increased awareness.`
                    },
                    {
                        heading: 'Practical Tips for Daily Integration',
                        content: `<strong>Start Small:</strong> Begin with just 5 minutes daily and gradually increase. Consistency is more important than duration.

<strong>Create a Routine:</strong> Practice at the same time each day to build a habit. Many find morning meditation sets a positive tone for the day.

<strong>Use Guided Resources:</strong> Apps, online videos, or local classes can provide structure and support for beginners.

<strong>Mindful Moments:</strong> Integrate mindfulness into daily activities like eating, walking, or even washing dishes. These micro-practices reinforce formal meditation.

<strong>Be Patient:</strong> Don't expect immediate results. Like physical exercise, the benefits of meditation compound over time with regular practice.`
                    },
                    {
                        heading: 'Real-World Applications and Conclusion',
                        content: `Mindfulness meditation extends far beyond formal sitting practice. It can be applied in workplace stress management, relationship communication, parenting, and academic performance. Healthcare professionals increasingly recommend it as a complementary treatment for various conditions.

The beauty of mindfulness meditation lies in its simplicity and accessibility. It requires no special equipment, can be practiced anywhere, and offers a practical tool for navigating life's challenges with greater ease and clarity. As you develop this practice, you'll likely find that the awareness cultivated during meditation naturally extends into your daily life, creating a more mindful, peaceful, and fulfilling existence.

Remember, mindfulness meditation is not about achieving a particular state of mind, but rather about learning to be present with whatever arises. This fundamental shift in perspective can transform how you relate to stress, emotions, and life itself.`
                    }
                ]
            },

            'climate change': {
                title: 'Climate Change: Understanding the Global Challenge and Pathways Forward',
                sections: [
                    {
                        heading: 'What is Climate Change?',
                        content: `Climate change refers to long-term shifts in global or regional climate patterns, primarily attributed to increased levels of greenhouse gases in the atmosphere due to human activities. While Earth's climate has naturally fluctuated throughout history, the current rate and scale of change is unprecedented in human civilization.

The primary driver is the enhanced greenhouse effect, where gases like carbon dioxide, methane, and nitrous oxide trap heat in the atmosphere. Since the Industrial Revolution began in the late 1700s, atmospheric CO2 levels have increased by over 40%, primarily from burning fossil fuels, deforestation, and industrial processes.`
                    },
                    {
                        heading: 'Scientific Evidence and Current Impacts',
                        content: `The scientific consensus on climate change is overwhelming, with 97% of actively publishing climate scientists agreeing that human activities are the primary cause of recent climate change. Evidence includes rising global temperatures, melting ice sheets, rising sea levels, and changing precipitation patterns.

Current impacts are already visible worldwide: more frequent and intense heatwaves, stronger hurricanes, prolonged droughts, flooding, and ecosystem disruptions. Arctic sea ice is declining at a rate of 13% per decade, and global sea levels have risen about 8-9 inches since 1880, with the rate of rise accelerating in recent decades.`
                    },
                    {
                        heading: 'Future Projections and Scenarios',
                        content: `Climate models project continued warming throughout the 21st century, with the extent depending on future greenhouse gas emissions. Under current policies, global temperatures could rise 2.7-3.1°C above pre-industrial levels by 2100.

Different scenarios paint varying pictures: aggressive emission reductions could limit warming to 1.5-2°C, while business-as-usual approaches could lead to 4-5°C warming. Higher warming scenarios would bring catastrophic impacts including widespread coastal flooding, extreme weather events, food and water security challenges, and massive ecosystem disruptions.`
                    },
                    {
                        heading: 'Solutions and Mitigation Strategies',
                        content: `Addressing climate change requires both mitigation (reducing emissions) and adaptation (preparing for impacts). Key mitigation strategies include:

<strong>Energy Transition:</strong> Rapidly scaling renewable energy sources like solar, wind, and hydroelectric power while phasing out fossil fuels.

<strong>Transportation:</strong> Electrifying vehicles, improving public transit, and developing sustainable aviation and shipping fuels.

<strong>Industry and Buildings:</strong> Improving energy efficiency, electrifying heating systems, and developing clean industrial processes.

<strong>Nature-Based Solutions:</strong> Protecting and restoring forests, wetlands, and other ecosystems that naturally store carbon.

<strong>Policy Measures:</strong> Carbon pricing, regulations, and international cooperation through agreements like the Paris Climate Accord.`
                    },
                    {
                        heading: 'Individual Actions and Global Cooperation',
                        content: `While systemic change is essential, individual actions collectively make a significant impact. Personal steps include reducing energy consumption, choosing sustainable transportation, eating less meat, minimizing waste, and supporting climate-conscious policies and businesses.

However, the scale of the challenge requires unprecedented global cooperation. Success depends on governments, businesses, and communities working together to implement solutions rapidly and equitably. This includes supporting developing nations in their transition to clean energy and helping vulnerable communities adapt to climate impacts.

The next decade is crucial for determining our climate future. While the challenge is enormous, we possess the technologies and knowledge needed to address climate change. What's required now is the collective will to implement solutions at the speed and scale necessary to protect our planet for future generations. The choices we make today will determine the world we leave behind.`
                    }
                ]
            },

            'healthy cooking': {
                title: 'Healthy Cooking: Transform Your Kitchen into a Wellness Hub',
                sections: [
                    {
                        heading: 'Foundations of Healthy Cooking',
                        content: `Healthy cooking is more than just following recipes—it's about understanding how to prepare foods that nourish your body while satisfying your taste buds. The foundation lies in choosing whole, minimally processed ingredients and using cooking methods that preserve nutrients while enhancing flavors.

The key principles include emphasizing fresh vegetables and fruits, incorporating lean proteins, choosing whole grains over refined ones, using healthy fats like olive oil and avocado, and minimizing added sugars and excessive sodium. Healthy cooking also means understanding portion sizes and creating balanced meals that provide sustained energy throughout the day.`
                    },
                    {
                        heading: 'Essential Cooking Techniques for Health',
                        content: `<strong>Steaming:</strong> Preserves nutrients and natural flavors without added fats. Perfect for vegetables, fish, and dumplings.

<strong>Roasting:</strong> Brings out natural sweetness in vegetables and creates delicious, caramelized flavors with minimal oil.

<strong>Grilling:</strong> Allows excess fats to drain away while adding smoky flavors. Great for lean meats, fish, and vegetables.

<strong>Sautéing:</strong> Quick cooking method using small amounts of healthy oils, perfect for preserving vegetable crispness and nutrients.

<strong>Poaching:</strong> Gentle cooking in liquid that keeps proteins tender and moist without added fats.

These methods help retain maximum nutritional value while creating delicious, satisfying meals that support your health goals.`
                    },
                    {
                        heading: 'Smart Ingredient Substitutions',
                        content: `Making healthier choices doesn't mean sacrificing flavor. Smart substitutions can dramatically improve the nutritional profile of your favorite dishes:

<strong>Grains:</strong> Replace white rice with quinoa, brown rice, or cauliflower rice. Use whole wheat pasta or zucchini noodles instead of regular pasta.

<strong>Fats:</strong> Substitute butter with avocado, Greek yogurt, or applesauce in baking. Use olive oil spray instead of butter for cooking.

<strong>Proteins:</strong> Try plant-based proteins like lentils, beans, or tofu. Choose lean cuts of meat and remove visible fat.

<strong>Dairy:</strong> Use Greek yogurt instead of sour cream, and try unsweetened almond or oat milk in place of whole milk.

<strong>Sweeteners:</strong> Replace sugar with natural alternatives like dates, honey, or stevia in moderation.`
                    },
                    {
                        heading: 'Meal Planning and Preparation Strategies',
                        content: `Successful healthy cooking starts with planning. Dedicate time each week to plan meals, create shopping lists, and prep ingredients. This approach saves time, reduces food waste, and helps you make healthier choices when you're busy.

<strong>Batch Cooking:</strong> Prepare large quantities of staples like grains, roasted vegetables, and proteins that can be mixed and matched throughout the week.

<strong>Prep Ahead:</strong> Wash and chop vegetables, marinate proteins, and portion snacks in advance.

<strong>Smart Storage:</strong> Invest in quality containers to keep prepped foods fresh and organized.

<strong>Flexible Recipes:</strong> Master versatile base recipes that can be customized with different vegetables, proteins, and seasonings.`
                    },
                    {
                        heading: 'Building Flavorful, Nutritious Meals',
                        content: `Healthy cooking doesn't mean bland food. Building layers of flavor through herbs, spices, and cooking techniques creates satisfying meals that support your wellness goals.

<strong>Herb and Spice Power:</strong> Fresh herbs like basil, cilantro, and parsley add vibrant flavors and antioxidants. Spices like turmeric, cumin, and paprika provide both flavor and health benefits.

<strong>Umami Elements:</strong> Incorporate naturally savory ingredients like mushrooms, tomatoes, and fermented foods to create depth and satisfaction.

<strong>Texture Variety:</strong> Combine different textures—crispy roasted vegetables, creamy avocado, crunchy nuts—to make meals more interesting and satisfying.

<strong>Color Diversity:</strong> Aim for colorful plates, as different colored foods provide various nutrients and antioxidants.

Remember, healthy cooking is a journey, not a destination. Start with small changes, experiment with new ingredients and techniques, and gradually build a repertoire of nutritious, delicious meals. The goal is to create sustainable habits that make healthy eating enjoyable and accessible, transforming your relationship with food and supporting your long-term wellness goals.`
                    }
                ]
            }
        };

        this.fallbackTemplate = {
            title: 'Comprehensive Guide to [TOPIC]',
            sections: [
                {
                    heading: 'Understanding [TOPIC]',
                    content: 'This section provides a foundational understanding of the topic, including key definitions, background information, and context that helps readers grasp the essential concepts.'
                },
                {
                    heading: 'Key Benefits and Importance',
                    content: 'Here we explore why this topic matters, the benefits it offers, and its significance in relevant contexts. This section helps readers understand the value and impact.'
                },
                {
                    heading: 'Practical Applications and Examples',
                    content: 'This section provides real-world examples, case studies, and practical applications that demonstrate how the concepts can be implemented or observed in everyday situations.'
                },
                {
                    heading: 'Step-by-Step Implementation Guide',
                    content: 'A detailed, actionable guide that breaks down the process into manageable steps, providing readers with a clear pathway to apply what they have learned.'
                },
                {
                    heading: 'Best Practices and Future Considerations',
                    content: 'This concluding section offers expert tips, best practices, common pitfalls to avoid, and considerations for future development or continued learning in this area.'
                }
            ]
        };
    }

    generateContent(query) {
        const startTime = Date.now();
        
        // Normalize query for matching
        const normalizedQuery = query.toLowerCase().trim();
        
        // Check for exact matches first
        if (this.contentTemplates[normalizedQuery]) {
            const content = this.contentTemplates[normalizedQuery];
            return this.formatContent(content, query, startTime);
        }

        // Check for partial matches
        for (const [key, template] of Object.entries(this.contentTemplates)) {
            if (normalizedQuery.includes(key) || key.includes(normalizedQuery)) {
                const content = this.contentTemplates[key];
                return this.formatContent(content, query, startTime);
            }
        }

        // Generate dynamic content for unknown topics
        return this.generateDynamicContent(query, startTime);
    }

    formatContent(template, query, startTime) {
        const generationTime = Date.now() - startTime;
        
        let html = `<div class="intro-section">
            <h1>${template.title}</h1>
        </div>`;

        template.sections.forEach(section => {
            html += `
                <h2>${section.heading}</h2>
                <p>${section.content}</p>
            `;
        });

        const wordCount = this.countWords(html);
        
        return {
            html: html,
            wordCount: wordCount,
            generationTime: generationTime
        };
    }

    generateDynamicContent(query, startTime) {
        const generationTime = Date.now() - startTime + Math.random() * 500; // Simulate processing time
        
        const capitalizedQuery = query.charAt(0).toUpperCase() + query.slice(1);
        
        let html = `<div class="intro-section">
            <h1>Comprehensive Guide to ${capitalizedQuery}</h1>
        </div>`;

        // Generate dynamic sections based on the query
        const sections = this.generateDynamicSections(query);
        
        sections.forEach(section => {
            html += `
                <h2>${section.heading}</h2>
                <p>${section.content}</p>
            `;
        });

        html += `<div class="conclusion-section">
            <h2>Conclusion</h2>
            <p>Understanding ${query} requires a comprehensive approach that considers multiple perspectives and practical applications. By exploring the various aspects covered in this guide, you now have a solid foundation to further explore and apply these concepts in your own context. Remember that knowledge grows through practice and continued learning, so consider this guide as a starting point for your journey with ${query}.</p>
        </div>`;

        const wordCount = this.countWords(html);
        
        return {
            html: html,
            wordCount: wordCount,
            generationTime: Math.round(generationTime)
        };
    }

    generateDynamicSections(query) {
        const sections = [];
        const queryWords = query.toLowerCase().split(' ');
        
        // Introduction section
        sections.push({
            heading: `Understanding ${query.charAt(0).toUpperCase() + query.slice(1)}`,
            content: `${query.charAt(0).toUpperCase() + query.slice(1)} is a multifaceted topic that encompasses various aspects and considerations. To fully grasp its significance, it's important to examine the fundamental concepts, underlying principles, and the context in which it operates. This comprehensive overview will provide you with the essential knowledge needed to understand and engage with this subject effectively.

The importance of understanding ${query} cannot be overstated in today's rapidly evolving world. Whether you're a beginner seeking foundational knowledge or someone looking to deepen your existing understanding, this guide will provide valuable insights and practical information that you can apply in real-world situations.`
        });

        // Benefits and importance
        sections.push({
            heading: `Key Benefits and Applications of ${query.charAt(0).toUpperCase() + query.slice(1)}`,
            content: `The benefits of engaging with ${query} extend across multiple dimensions, offering both immediate and long-term advantages. Research and practical experience have shown that understanding and applying concepts related to ${query} can lead to improved outcomes, enhanced decision-making capabilities, and greater overall effectiveness.

From a practical standpoint, ${query} offers numerous applications that can be implemented in various settings. These applications range from personal development and professional growth to broader societal and organizational benefits. The versatility of ${query} makes it a valuable area of study and practice for individuals from diverse backgrounds and with varying objectives.`
        });

        // Practical implementation
        sections.push({
            heading: `Practical Implementation Strategies`,
            content: `Implementing ${query} effectively requires a systematic approach that considers both theoretical understanding and practical application. The most successful implementations typically follow a structured methodology that includes careful planning, gradual implementation, and continuous evaluation and adjustment.

<strong>Getting Started:</strong> Begin by establishing clear objectives and understanding your current baseline. This foundation will help you measure progress and make informed decisions throughout the implementation process.

<strong>Step-by-Step Approach:</strong> Break down the implementation into manageable phases, allowing for learning and adaptation at each stage. This approach reduces risk and increases the likelihood of successful outcomes.

<strong>Monitoring and Evaluation:</strong> Regularly assess your progress and be prepared to make adjustments based on what you learn. Flexibility and responsiveness are key to successful implementation.`
        });

        // Best practices and tips
        sections.push({
            heading: `Best Practices and Expert Recommendations`,
            content: `Based on extensive research and practical experience, several best practices have emerged for maximizing the benefits of ${query}. These recommendations come from experts in the field and individuals who have successfully implemented these concepts in various contexts.

<strong>Consistency is Key:</strong> Regular, consistent engagement with ${query} tends to produce better results than sporadic, intensive efforts. Develop sustainable practices that you can maintain over time.

<strong>Seek Continuous Learning:</strong> The field related to ${query} is constantly evolving. Stay informed about new developments, research findings, and emerging best practices to ensure your approach remains current and effective.

<strong>Community and Support:</strong> Connect with others who share your interest in ${query}. Learning from others' experiences and sharing your own insights can accelerate your progress and provide valuable support.`
        });

        return sections;
    }

    countWords(html) {
        // Remove HTML tags and count words
        const text = html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
        return text.split(' ').filter(word => word.length > 0).length;
    }

    getSuggestions(query) {
        const suggestions = [
            'mindfulness meditation',
            'climate change',
            'healthy cooking',
            'artificial intelligence',
            'sustainable living',
            'digital marketing',
            'personal finance',
            'time management',
            'renewable energy',
            'mental health',
            'nutrition basics',
            'exercise science',
            'productivity tips',
            'stress management',
            'learning techniques'
        ];

        if (!query) return suggestions.slice(0, 5);

        const filtered = suggestions.filter(suggestion => 
            suggestion.toLowerCase().includes(query.toLowerCase())
        );

        return filtered.slice(0, 5);
    }
}

// Export for use in main script
window.ContentGenerator = ContentGenerator;
