// Business Pitch Generator Module
class BusinessPitchGenerator {
    constructor() {
        this.industryTemplates = {
            technology: {
                keywords: ['innovative', 'cutting-edge', 'scalable', 'digital transformation', 'automation', 'AI-powered'],
                painPoints: ['inefficient processes', 'outdated systems', 'manual workflows', 'data silos'],
                solutions: ['streamlined operations', 'automated workflows', 'real-time analytics', 'seamless integration']
            },
            ecommerce: {
                keywords: ['customer-centric', 'seamless shopping', 'personalized experience', 'omnichannel'],
                painPoints: ['poor user experience', 'limited product discovery', 'complex checkout process'],
                solutions: ['intuitive interface', 'personalized recommendations', 'streamlined purchasing']
            },
            healthcare: {
                keywords: ['patient-focused', 'evidence-based', 'accessible care', 'health outcomes'],
                painPoints: ['fragmented care', 'limited access', 'inefficient processes', 'poor communication'],
                solutions: ['coordinated care', 'improved accessibility', 'better patient outcomes']
            },
            finance: {
                keywords: ['secure', 'transparent', 'efficient', 'compliant', 'data-driven'],
                painPoints: ['complex processes', 'security concerns', 'regulatory compliance', 'limited transparency'],
                solutions: ['simplified processes', 'enhanced security', 'regulatory compliance', 'transparent operations']
            },
            education: {
                keywords: ['engaging', 'personalized learning', 'accessible', 'skill development'],
                painPoints: ['one-size-fits-all approach', 'limited engagement', 'accessibility barriers'],
                solutions: ['personalized learning paths', 'interactive content', 'improved accessibility']
            }
        };

        this.pitchStructures = {
            investor: {
                sections: ['Problem & Market Opportunity', 'Solution & Value Proposition', 'Business Model & Revenue', 'Market Traction & Growth', 'Investment & Returns'],
                focus: 'ROI and scalability'
            },
            customer: {
                sections: ['Your Challenge', 'Our Solution', 'Key Benefits', 'Why Choose Us', 'Next Steps'],
                focus: 'value and benefits'
            },
            partner: {
                sections: ['Market Opportunity', 'Partnership Value', 'Mutual Benefits', 'Success Stories', 'Collaboration Framework'],
                focus: 'mutual growth'
            },
            general: {
                sections: ['Company Overview', 'Products & Services', 'Market Position', 'Competitive Advantages', 'Future Vision'],
                focus: 'comprehensive overview'
            }
        };
    }

    generatePitch(formData) {
        const startTime = Date.now();
        const { brandName, businessType, businessDescription, pitchType, targetAudience } = formData;

        // Get industry-specific data
        const industryData = this.industryTemplates[businessType] || this.industryTemplates.technology;
        const pitchStructure = this.pitchStructures[pitchType];

        // Generate comprehensive pitch
        const pitch = this.createPitchContent(brandName, businessType, businessDescription, pitchType, targetAudience, industryData, pitchStructure);

        return {
            html: pitch,
            wordCount: this.countWords(pitch),
            generationTime: Date.now() - startTime,
            source: 'AI Business Pitch Generator'
        };
    }

    createPitchContent(brandName, businessType, description, pitchType, targetAudience, industryData, structure) {
        const capitalizedBrand = brandName.charAt(0).toUpperCase() + brandName.slice(1);
        const industryName = this.getIndustryDisplayName(businessType);
        
        let content = `
            <div class="intro-section">
                <h1>${capitalizedBrand}: ${this.generateTagline(brandName, businessType, industryData)}</h1>
                <p>A comprehensive ${pitchType} pitch for ${capitalizedBrand}, ${this.generateCompanyDescription(businessType, description, industryData)}</p>
            </div>
        `;

        // Generate sections based on pitch type
        switch (pitchType) {
            case 'investor':
                content += this.generateInvestorPitch(capitalizedBrand, businessType, description, targetAudience, industryData);
                break;
            case 'customer':
                content += this.generateCustomerPitch(capitalizedBrand, businessType, description, targetAudience, industryData);
                break;
            case 'partner':
                content += this.generatePartnerPitch(capitalizedBrand, businessType, description, targetAudience, industryData);
                break;
            default:
                content += this.generateGeneralPitch(capitalizedBrand, businessType, description, targetAudience, industryData);
        }

        content += `
            <div class="conclusion-section">
                <h2>Ready to Move Forward</h2>
                <p>${capitalizedBrand} represents a compelling opportunity in the ${industryName} sector. Our ${this.getRandomElement(industryData.keywords)} approach, combined with deep market understanding and proven execution capabilities, positions us for significant growth and success.</p>
                <p><strong>Contact us today to learn more about how ${capitalizedBrand} can ${this.generateCallToAction(pitchType, targetAudience)}.</strong></p>
            </div>
        `;

        return content;
    }

    generateInvestorPitch(brandName, businessType, description, targetAudience, industryData) {
        return `
            <h2>Problem & Market Opportunity</h2>
            <p>The ${this.getIndustryDisplayName(businessType)} industry faces significant challenges including ${industryData.painPoints.join(', ')}. These issues create substantial friction and limit growth potential for businesses and consumers alike.</p>
            <p>Market research indicates a ${this.generateMarketSize(businessType)} market opportunity, with growing demand for ${this.getRandomElement(industryData.keywords)} solutions. ${brandName} is positioned to capture significant market share by addressing these critical pain points.</p>

            <h2>Solution & Value Proposition</h2>
            <p>${brandName} delivers ${industryData.solutions.join(', ')} through our ${this.getRandomElement(industryData.keywords)} platform. Our solution directly addresses market needs while providing measurable value to ${targetAudience || 'our target customers'}.</p>
            <p><strong>Key Differentiators:</strong> ${this.generateDifferentiators(businessType, industryData)}</p>

            <h2>Business Model & Revenue Streams</h2>
            <p>Our revenue model is built on ${this.generateRevenueModel(businessType)}, ensuring sustainable growth and scalability. We project ${this.generateGrowthProjections()} based on current market trends and our go-to-market strategy.</p>
            <p><strong>Monetization Strategy:</strong> ${this.generateMonetizationStrategy(businessType)}</p>

            <h2>Market Traction & Growth Metrics</h2>
            <p>${brandName} has demonstrated strong early traction with ${this.generateTractionMetrics(businessType)}. Our growth strategy focuses on ${this.generateGrowthStrategy(businessType, targetAudience)}.</p>
            <p><strong>Competitive Advantage:</strong> ${this.generateCompetitiveAdvantage(businessType, industryData)}</p>

            <h2>Investment Opportunity & Returns</h2>
            <p>We are seeking investment to ${this.generateFundingUse(businessType)}. This funding will enable us to ${this.generateScalingPlan(businessType)} and achieve our projected ${this.generateROIProjection()} return for investors.</p>
            <p><strong>Use of Funds:</strong> ${this.generateDetailedFundingUse(businessType)}</p>
        `;
    }

    generateCustomerPitch(brandName, businessType, description, targetAudience, industryData) {
        return `
            <h2>Your Challenge</h2>
            <p>As ${targetAudience || 'a business in today\'s competitive market'}, you likely face challenges such as ${industryData.painPoints.join(', ')}. These issues can significantly impact your productivity, costs, and overall success.</p>
            <p>${brandName} understands these challenges because we've helped numerous organizations overcome similar obstacles and achieve remarkable results.</p>

            <h2>Our Solution</h2>
            <p>${brandName} provides ${this.getRandomElement(industryData.keywords)} solutions that deliver ${industryData.solutions.join(', ')}. Our approach is specifically designed for ${targetAudience || 'businesses like yours'} who need reliable, effective results.</p>
            <p><strong>What We Offer:</strong> ${this.generateServiceOfferings(businessType, industryData)}</p>

            <h2>Key Benefits You'll Experience</h2>
            <p>By choosing ${brandName}, you'll benefit from:</p>
            <ul>
                <li><strong>Immediate Impact:</strong> ${this.generateImmediateBenefit(businessType)}</li>
                <li><strong>Long-term Value:</strong> ${this.generateLongTermBenefit(businessType)}</li>
                <li><strong>Competitive Edge:</strong> ${this.generateCompetitiveEdge(businessType, industryData)}</li>
                <li><strong>Cost Efficiency:</strong> ${this.generateCostBenefit(businessType)}</li>
            </ul>

            <h2>Why Choose ${brandName}</h2>
            <p>Unlike other solutions in the market, ${brandName} offers ${this.generateUniqueValue(businessType, industryData)}. Our track record includes ${this.generateSuccessMetrics(businessType)} and consistently positive feedback from clients.</p>
            <p><strong>Client Success:</strong> ${this.generateClientSuccess(businessType)}</p>

            <h2>Next Steps</h2>
            <p>Ready to transform your ${this.getIndustryDisplayName(businessType)} operations? ${brandName} makes it easy to get started with ${this.generateOnboardingProcess(businessType)}.</p>
            <p><strong>Get Started Today:</strong> ${this.generateActionSteps(businessType)}</p>
        `;
    }

    generatePartnerPitch(brandName, businessType, description, targetAudience, industryData) {
        return `
            <h2>Market Opportunity</h2>
            <p>The ${this.getIndustryDisplayName(businessType)} market presents significant opportunities for strategic partnerships. By combining our strengths, we can address ${industryData.painPoints.join(', ')} more effectively than either organization could alone.</p>
            <p>${brandName} brings ${this.generatePartnershipAssets(businessType, industryData)} to create mutual value and market expansion opportunities.</p>

            <h2>Partnership Value Proposition</h2>
            <p>A partnership with ${brandName} offers ${this.generatePartnershipBenefits(businessType, industryData)}. Together, we can deliver ${industryData.solutions.join(', ')} while expanding our respective market reach.</p>
            <p><strong>Strategic Alignment:</strong> ${this.generateStrategicAlignment(businessType)}</p>

            <h2>Mutual Benefits & Synergies</h2>
            <p>This partnership creates win-win opportunities through:</p>
            <ul>
                <li><strong>Market Expansion:</strong> ${this.generateMarketExpansion(businessType)}</li>
                <li><strong>Resource Optimization:</strong> ${this.generateResourceOptimization(businessType)}</li>
                <li><strong>Innovation Acceleration:</strong> ${this.generateInnovationBenefits(businessType, industryData)}</li>
                <li><strong>Risk Mitigation:</strong> ${this.generateRiskMitigation(businessType)}</li>
            </ul>

            <h2>Success Framework</h2>
            <p>${brandName} proposes a structured partnership approach that includes ${this.generatePartnershipFramework(businessType)}. Our collaborative model ensures ${this.generateSuccessMetrics(businessType)} for both organizations.</p>
            <p><strong>Implementation Plan:</strong> ${this.generateImplementationPlan(businessType)}</p>

            <h2>Collaboration Roadmap</h2>
            <p>We envision a partnership that evolves through ${this.generatePartnershipPhases(businessType)}. This phased approach allows us to build trust, demonstrate value, and scale our collaboration effectively.</p>
            <p><strong>Next Steps:</strong> ${this.generatePartnershipNextSteps()}</p>
        `;
    }

    generateGeneralPitch(brandName, businessType, description, targetAudience, industryData) {
        return `
            <h2>Company Overview</h2>
            <p>${brandName} is a ${this.getRandomElement(industryData.keywords)} company in the ${this.getIndustryDisplayName(businessType)} sector. We specialize in delivering ${industryData.solutions.join(', ')} to ${targetAudience || 'our diverse client base'}.</p>
            <p>Founded on the principle of ${this.generateFoundingPrinciple(businessType, industryData)}, ${brandName} has established itself as a trusted partner for organizations seeking ${this.getRandomElement(industryData.keywords)} solutions.</p>

            <h2>Products & Services</h2>
            <p>Our comprehensive offering includes ${this.generateProductServices(businessType, industryData)}. Each solution is designed to address specific challenges while delivering measurable results for our clients.</p>
            <p><strong>Core Offerings:</strong> ${this.generateCoreOfferings(businessType, industryData)}</p>

            <h2>Market Position & Competitive Landscape</h2>
            <p>${brandName} operates in a ${this.generateMarketDescription(businessType)} market, where we've established a strong position through ${this.generateMarketPosition(businessType, industryData)}.</p>
            <p><strong>Competitive Advantages:</strong> ${this.generateCompetitiveAdvantages(businessType, industryData)}</p>

            <h2>Our Approach & Methodology</h2>
            <p>What sets ${brandName} apart is our ${this.getRandomElement(industryData.keywords)} approach to ${this.getIndustryDisplayName(businessType)}. We combine ${this.generateMethodology(businessType, industryData)} to deliver exceptional results.</p>
            <p><strong>Success Factors:</strong> ${this.generateSuccessFactors(businessType, industryData)}</p>

            <h2>Future Vision & Growth Strategy</h2>
            <p>Looking ahead, ${brandName} is positioned for significant growth through ${this.generateGrowthStrategy(businessType, targetAudience)}. Our vision is to ${this.generateVision(businessType, industryData)} while maintaining our commitment to excellence.</p>
            <p><strong>Strategic Initiatives:</strong> ${this.generateStrategicInitiatives(businessType)}</p>
        `;
    }

    // Helper methods for generating dynamic content
    generateTagline(brandName, businessType, industryData) {
        const keywords = industryData.keywords;
        return `${this.getRandomElement(keywords)} solutions for ${this.getIndustryDisplayName(businessType)} excellence`;
    }

    generateCompanyDescription(businessType, description, industryData) {
        if (description && description.trim()) {
            return `focused on ${description.trim()}`;
        }
        return `delivering ${this.getRandomElement(industryData.keywords)} solutions in the ${this.getIndustryDisplayName(businessType)} industry`;
    }

    getIndustryDisplayName(businessType) {
        const displayNames = {
            technology: 'technology',
            ecommerce: 'e-commerce',
            healthcare: 'healthcare',
            finance: 'financial services',
            education: 'education',
            food: 'food & beverage',
            fashion: 'fashion & beauty',
            'real-estate': 'real estate',
            consulting: 'consulting',
            manufacturing: 'manufacturing',
            entertainment: 'entertainment',
            travel: 'travel & tourism',
            fitness: 'fitness & wellness',
            automotive: 'automotive',
            other: 'business'
        };
        return displayNames[businessType] || 'business';
    }

    getRandomElement(array) {
        return array[Math.floor(Math.random() * array.length)];
    }

    generateMarketSize(businessType) {
        const sizes = ['$2.5 billion', '$5.8 billion', '$12.3 billion', '$8.7 billion', '$15.2 billion'];
        return this.getRandomElement(sizes);
    }

    generateDifferentiators(businessType, industryData) {
        return `${this.getRandomElement(industryData.keywords)} technology, proven track record, and customer-centric approach`;
    }

    generateRevenueModel(businessType) {
        const models = {
            technology: 'subscription-based SaaS model with tiered pricing',
            ecommerce: 'transaction fees and premium service subscriptions',
            healthcare: 'service fees and outcome-based pricing',
            finance: 'transaction processing and advisory fees',
            education: 'subscription and licensing models'
        };
        return models[businessType] || 'diversified revenue streams including service fees and subscriptions';
    }

    generateCallToAction(pitchType, targetAudience) {
        const actions = {
            investor: 'drive exceptional returns and market growth',
            customer: 'transform your operations and achieve your goals',
            partner: 'create mutual value and market expansion',
            general: 'achieve sustainable growth and success'
        };
        return actions[pitchType] || 'achieve your business objectives';
    }

    countWords(html) {
        const text = html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
        return text.split(' ').filter(word => word.length > 0).length;
    }

    // Additional helper methods for generating specific content sections
    generateGrowthProjections() {
        return '300-500% revenue growth over the next 3 years';
    }

    generateMonetizationStrategy(businessType) {
        return `Multi-tiered pricing strategy with premium features and enterprise solutions`;
    }

    generateTractionMetrics(businessType) {
        return 'strong customer acquisition rates, positive user feedback, and growing market presence';
    }

    generateFundingUse(businessType) {
        return 'accelerate product development, expand market reach, and scale operations';
    }

    generateROIProjection() {
        return '5-10x';
    }

    generateDetailedFundingUse(businessType) {
        return '40% product development, 35% marketing and sales, 15% operations, 10% working capital';
    }

    generateServiceOfferings(businessType, industryData) {
        return `Comprehensive ${this.getRandomElement(industryData.keywords)} solutions tailored to your specific needs`;
    }

    generateImmediateBenefit(businessType) {
        return 'See results within the first 30 days of implementation';
    }

    generateLongTermBenefit(businessType) {
        return 'Sustainable growth and competitive advantage over time';
    }

    generateCompetitiveEdge(businessType, industryData) {
        return `Stay ahead with our ${this.getRandomElement(industryData.keywords)} approach`;
    }

    generateCostBenefit(businessType) {
        return 'Reduce operational costs by 20-40% while improving efficiency';
    }

    generateUniqueValue(businessType, industryData) {
        return `${this.getRandomElement(industryData.keywords)} technology combined with personalized service`;
    }

    generateSuccessMetrics(businessType) {
        return '95% client satisfaction rate and measurable ROI improvements';
    }

    generateClientSuccess(businessType) {
        return 'Our clients typically see 25-50% improvement in key performance metrics';
    }

    generateOnboardingProcess(businessType) {
        return 'a streamlined onboarding process and dedicated support team';
    }

    generateActionSteps(businessType) {
        return 'Schedule a consultation, receive a customized proposal, and begin implementation within 2 weeks';
    }

    generatePartnershipAssets(businessType, industryData) {
        return `${this.getRandomElement(industryData.keywords)} expertise, established market presence, and proven execution capabilities`;
    }

    generatePartnershipBenefits(businessType, industryData) {
        return `enhanced market reach, shared resources, and accelerated ${this.getRandomElement(industryData.keywords)} innovation`;
    }

    generateStrategicAlignment(businessType) {
        return 'Complementary strengths and shared vision for market leadership';
    }

    generateMarketExpansion(businessType) {
        return 'Access to new customer segments and geographic markets';
    }

    generateResourceOptimization(businessType) {
        return 'Shared costs, combined expertise, and operational efficiencies';
    }

    generateInnovationBenefits(businessType, industryData) {
        return `Accelerated ${this.getRandomElement(industryData.keywords)} development and faster time-to-market`;
    }

    generateRiskMitigation(businessType) {
        return 'Diversified market exposure and shared investment risks';
    }

    generatePartnershipFramework(businessType) {
        return 'clear governance structure, defined roles, and performance metrics';
    }

    generateImplementationPlan(businessType) {
        return 'Phased rollout with milestone-based evaluation and continuous optimization';
    }

    generatePartnershipPhases(businessType) {
        return 'pilot collaboration, scaled implementation, and strategic expansion';
    }

    generatePartnershipNextSteps() {
        return 'Initial discussion, partnership agreement, and pilot project launch';
    }

    generateFoundingPrinciple(businessType, industryData) {
        return `delivering ${this.getRandomElement(industryData.keywords)} value to every client`;
    }

    generateProductServices(businessType, industryData) {
        return `${this.getRandomElement(industryData.keywords)} solutions designed to deliver ${this.getRandomElement(industryData.solutions)}`;
    }

    generateCoreOfferings(businessType, industryData) {
        return industryData.solutions.map(solution => solution.charAt(0).toUpperCase() + solution.slice(1)).join(', ');
    }

    generateMarketDescription(businessType) {
        return 'rapidly evolving and highly competitive';
    }

    generateMarketPosition(businessType, industryData) {
        return `our ${this.getRandomElement(industryData.keywords)} approach and commitment to excellence`;
    }

    generateCompetitiveAdvantages(businessType, industryData) {
        return `${this.getRandomElement(industryData.keywords)} technology, experienced team, and proven methodology`;
    }

    generateMethodology(businessType, industryData) {
        return `industry best practices with ${this.getRandomElement(industryData.keywords)} innovation`;
    }

    generateSuccessFactors(businessType, industryData) {
        return `Client focus, ${this.getRandomElement(industryData.keywords)} execution, and continuous improvement`;
    }

    generateVision(businessType, industryData) {
        return `become the leading provider of ${this.getRandomElement(industryData.keywords)} solutions in our market`;
    }

    generateStrategicInitiatives(businessType) {
        return 'Market expansion, product innovation, and strategic partnerships';
    }
}

// Export for use in main script
window.BusinessPitchGenerator = BusinessPitchGenerator;
