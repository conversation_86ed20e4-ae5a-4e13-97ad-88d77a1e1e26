<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KnowledgeSearch - AI-Powered Content Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <div class="logo">
                <h1>KnowledgeSearch</h1>
                <p class="tagline">✨ Comprehensive answers, no external links ✨</p>
            </div>
        </header>

        <!-- Search Section -->
        <main class="main-content">
            <!-- Search Mode Toggle -->
            <div class="search-mode-toggle" id="searchModeToggle">
                <button class="mode-btn active" data-mode="text" id="textModeBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14,2 14,8 20,8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10,9 9,9 8,9"></polyline>
                    </svg>
                    Text Search
                </button>
                <button class="mode-btn" data-mode="image" id="imageModeBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21,15 16,10 5,21"></polyline>
                    </svg>
                    Image Analysis
                </button>
            </div>

            <!-- Text Search Container -->
            <div class="search-container" id="textSearchContainer">
                <div class="search-box">
                    <input
                        type="text"
                        id="searchInput"
                        placeholder="Enter your query (e.g., mindfulness meditation, climate change, healthy cooking...)"
                        autocomplete="off"
                    >
                    <button id="searchButton" class="search-btn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                    </button>
                </div>
                <div class="search-suggestions" id="searchSuggestions"></div>
            </div>

            <!-- Image Upload Container -->
            <div class="image-upload-container" id="imageUploadContainer" style="display: none;">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <svg class="upload-icon" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21,15 16,10 5,21"></polyline>
                        </svg>
                        <h3>Upload an Image for AI Analysis</h3>
                        <p>Drag and drop an image here, or click to select</p>
                        <p class="upload-formats">Supports: JPG, PNG, GIF, WebP (Max 10MB)</p>
                        <button class="upload-btn" id="uploadBtn">Choose Image</button>
                    </div>
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">
                </div>

                <!-- Image Preview -->
                <div class="image-preview" id="imagePreview" style="display: none;">
                    <div class="preview-container">
                        <img id="previewImage" alt="Preview">
                        <div class="preview-actions">
                            <button class="analyze-btn" id="analyzeBtn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M9 12l2 2 4-4"></path>
                                    <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                                    <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                                </svg>
                                Analyze Image
                            </button>
                            <button class="remove-btn" id="removeBtn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="3,6 5,6 21,6"></polyline>
                                    <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                                </svg>
                                Remove
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div class="loading" id="loadingState" style="display: none;">
                <div class="loading-spinner"></div>
                <p>Generating comprehensive content for your query...</p>
            </div>

            <!-- Results Section -->
            <div class="results-container" id="resultsContainer" style="display: none;">
                <div class="results-header">
                    <button id="newSearchBtn" class="new-search-btn">New Search</button>
                    <div class="query-display">
                        <span class="query-label">Your Query:</span>
                        <span id="currentQuery" class="current-query"></span>
                    </div>
                </div>

                <article class="blog-content" id="blogContent">
                    <!-- Generated content will be inserted here -->
                </article>

                <div class="content-footer">
                    <div class="word-count">
                        <span id="wordCount">0</span> words
                    </div>
                    <div class="generation-time">
                        Generated in <span id="generationTime">0</span>ms
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 KnowledgeSearch. Providing comprehensive, self-contained information.</p>
        </footer>
    </div>

    <script src="content-generator.js"></script>
    <script src="image-analyzer.js"></script>
    <script src="script.js"></script>
</body>
</html>
