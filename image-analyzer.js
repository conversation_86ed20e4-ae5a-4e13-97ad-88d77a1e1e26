// Image Analysis Module
class ImageAnalyzer {
    constructor() {
        this.apiKey = 'YOUR_API_KEY'; // Users will need to add their own API key
        this.apiEndpoint = 'https://api.openai.com/v1/chat/completions';
        this.fallbackAnalysis = true; // Enable fallback analysis when API is not available
    }

    // Convert image to base64
    async imageToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    // Analyze image using OpenAI Vision API (requires API key)
    async analyzeWithOpenAI(imageBase64) {
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                    model: "gpt-4-vision-preview",
                    messages: [
                        {
                            role: "user",
                            content: [
                                {
                                    type: "text",
                                    text: "Please analyze this image in detail. Provide comprehensive information about what you see, including objects, people, activities, setting, colors, composition, and any other relevant details. Format your response as a detailed analysis suitable for a knowledge search engine."
                                },
                                {
                                    type: "image_url",
                                    image_url: {
                                        url: imageBase64
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens: 1000
                })
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }

            const data = await response.json();
            return data.choices[0].message.content;
        } catch (error) {
            console.error('OpenAI API Error:', error);
            throw error;
        }
    }

    // Fallback analysis using image metadata and basic detection
    async fallbackImageAnalysis(file, imageElement) {
        const analysis = {
            fileName: file.name,
            fileSize: this.formatFileSize(file.size),
            fileType: file.type,
            dimensions: {
                width: imageElement.naturalWidth,
                height: imageElement.naturalHeight
            },
            aspectRatio: (imageElement.naturalWidth / imageElement.naturalHeight).toFixed(2)
        };

        // Generate comprehensive analysis
        return this.generateFallbackContent(analysis);
    }

    generateFallbackContent(analysis) {
        const { fileName, fileSize, fileType, dimensions, aspectRatio } = analysis;

        // Determine image category based on dimensions and aspect ratio
        let category = 'General Image';
        let orientation = 'Square';

        if (aspectRatio > 1.5) {
            orientation = 'Landscape';
            category = 'Landscape/Wide Image';
        } else if (aspectRatio < 0.7) {
            orientation = 'Portrait';
            category = 'Portrait/Tall Image';
        }

        // Determine quality based on dimensions
        let quality = 'Standard';
        const totalPixels = dimensions.width * dimensions.height;
        if (totalPixels > 2000000) quality = 'High Resolution';
        else if (totalPixels < 500000) quality = 'Low Resolution';

        // Get social media platform analysis
        const socialMediaAnalysis = this.getSocialMediaAnalysis(dimensions, aspectRatio);

        // Generate format-specific insights
        let formatInsights = '';
        if (fileType.includes('jpeg') || fileType.includes('jpg')) {
            formatInsights = 'JPEG format is ideal for photographs with many colors and gradients. This format uses lossy compression to reduce file size while maintaining visual quality.';
        } else if (fileType.includes('png')) {
            formatInsights = 'PNG format supports transparency and uses lossless compression. This format is excellent for graphics, logos, and images requiring sharp edges.';
        } else if (fileType.includes('gif')) {
            formatInsights = 'GIF format supports animation and uses a limited color palette. This format is commonly used for simple animations and graphics.';
        } else if (fileType.includes('webp')) {
            formatInsights = 'WebP is a modern format that provides superior compression and quality compared to JPEG and PNG, supporting both lossy and lossless compression.';
        }

        return `
            <div class="intro-section">
                <h1>Image Analysis: ${fileName}</h1>
                <p>Comprehensive analysis of the uploaded image including technical specifications, format details, and visual characteristics.</p>
            </div>

            <h2>Technical Specifications</h2>
            <p><strong>File Name:</strong> ${fileName}</p>
            <p><strong>File Size:</strong> ${fileSize}</p>
            <p><strong>Format:</strong> ${fileType.toUpperCase()}</p>
            <p><strong>Dimensions:</strong> ${dimensions.width} × ${dimensions.height} pixels</p>
            <p><strong>Aspect Ratio:</strong> ${aspectRatio}:1 (${orientation})</p>
            <p><strong>Resolution Quality:</strong> ${quality}</p>
            <p><strong>Total Pixels:</strong> ${totalPixels.toLocaleString()}</p>

            <h2>Format Analysis</h2>
            <p>${formatInsights}</p>
            <p>The image format affects both file size and quality. Understanding format characteristics helps in choosing the right format for specific use cases such as web display, printing, or archival storage.</p>

            <h2>Visual Characteristics</h2>
            <p><strong>Image Category:</strong> ${category}</p>
            <p><strong>Orientation:</strong> ${orientation} orientation images are ${this.getOrientationDescription(orientation)}</p>
            <p><strong>Display Suitability:</strong> ${this.getDisplaySuitability(dimensions, aspectRatio)}</p>

            <h2>Usage Recommendations</h2>
            <p>${this.getUsageRecommendations(fileType, dimensions, aspectRatio)}</p>

            <h2>Social Media Platform Compatibility</h2>
            <p>${socialMediaAnalysis.overview}</p>

            <h3>Platform-Specific Recommendations</h3>
            ${socialMediaAnalysis.platforms.map(platform => `
                <div class="platform-recommendation">
                    <p><strong>${platform.name}:</strong> ${platform.recommendation}</p>
                    ${platform.details ? `<p><em>${platform.details}</em></p>` : ''}
                </div>
            `).join('')}

            <h3>Social Media Optimization Tips</h3>
            <p>${socialMediaAnalysis.optimizationTips}</p>

            <h2>Technical Optimization Suggestions</h2>
            <p>${this.getOptimizationSuggestions(fileSize, fileType, totalPixels)}</p>

            <div class="conclusion-section">
                <h2>Summary</h2>
                <p>This ${quality.toLowerCase()} ${orientation.toLowerCase()} image in ${fileType.toUpperCase()} format provides ${this.getQualityAssessment(totalPixels)}. The technical specifications indicate it is ${this.getSuitabilityAssessment(dimensions, fileSize)} for most digital applications.</p>
                <p><strong>Best Social Media Fit:</strong> ${socialMediaAnalysis.bestFit}</p>
                <p><em>Note: For detailed visual content analysis including object recognition, scene description, and contextual information, please consider using an AI vision service with your API key.</em></p>
            </div>
        `;
    }

    getOrientationDescription(orientation) {
        switch (orientation) {
            case 'Landscape':
                return 'well-suited for wide scenes, panoramas, and desktop wallpapers.';
            case 'Portrait':
                return 'ideal for mobile displays, social media posts, and vertical compositions.';
            default:
                return 'versatile for various display contexts and social media platforms.';
        }
    }

    getDisplaySuitability(dimensions, aspectRatio) {
        if (dimensions.width >= 1920 && dimensions.height >= 1080) {
            return 'Excellent for high-resolution displays, printing, and professional use.';
        } else if (dimensions.width >= 1280 && dimensions.height >= 720) {
            return 'Good for web display, social media, and standard digital use.';
        } else {
            return 'Suitable for thumbnails, icons, and low-bandwidth applications.';
        }
    }

    getUsageRecommendations(fileType, dimensions, aspectRatio) {
        let recommendations = [];

        if (fileType.includes('jpeg')) {
            recommendations.push('Ideal for photographs and images with complex color gradients.');
        }
        if (fileType.includes('png')) {
            recommendations.push('Perfect for graphics requiring transparency or sharp edges.');
        }
        if (dimensions.width >= 1920) {
            recommendations.push('Suitable for high-quality printing and professional presentations.');
        }
        if (aspectRatio > 1.5) {
            recommendations.push('Excellent for banner images, headers, and landscape photography.');
        }
        if (aspectRatio < 0.8) {
            recommendations.push('Great for mobile-first designs and social media stories.');
        }

        return recommendations.join(' ') || 'This image format and dimensions make it versatile for various digital applications.';
    }

    getOptimizationSuggestions(fileSize, fileType, totalPixels) {
        let suggestions = [];

        if (fileSize > 5000000) { // 5MB
            suggestions.push('Consider compressing the image to reduce file size for web use.');
        }
        if (fileType.includes('png') && totalPixels > 1000000) {
            suggestions.push('For photographs, JPEG format might provide better compression.');
        }
        if (totalPixels > 4000000) {
            suggestions.push('Consider creating multiple sizes for responsive web design.');
        }
        if (!fileType.includes('webp')) {
            suggestions.push('WebP format could provide better compression while maintaining quality.');
        }

        return suggestions.join(' ') || 'The current format and size are well-optimized for most use cases.';
    }

    getQualityAssessment(totalPixels) {
        if (totalPixels > 8000000) return 'exceptional detail and clarity suitable for professional applications';
        if (totalPixels > 2000000) return 'good detail and quality for most digital uses';
        if (totalPixels > 500000) return 'adequate quality for web and social media';
        return 'basic quality suitable for thumbnails and low-resolution displays';
    }

    getSuitabilityAssessment(dimensions, fileSize) {
        if (dimensions.width >= 1920 && fileSize < 2000000) return 'well-optimized';
        if (dimensions.width >= 1280) return 'suitable';
        return 'appropriate';
    }

    getSocialMediaAnalysis(dimensions, aspectRatio) {
        const { width, height } = dimensions;
        const platforms = [];
        let bestFit = '';
        let bestScore = 0;

        // Define platform specifications and scoring
        const platformSpecs = {
            'Instagram Feed Post': {
                idealRatio: 1.0,
                minWidth: 1080,
                maxWidth: 1080,
                ratioTolerance: 0.1,
                description: 'Square format (1:1 ratio)'
            },
            'Instagram Stories': {
                idealRatio: 0.5625, // 9:16
                minWidth: 1080,
                maxWidth: 1080,
                ratioTolerance: 0.1,
                description: 'Vertical format (9:16 ratio)'
            },
            'Instagram Reels': {
                idealRatio: 0.5625, // 9:16
                minWidth: 1080,
                maxWidth: 1080,
                ratioTolerance: 0.1,
                description: 'Vertical video format (9:16 ratio)'
            },
            'Facebook Feed Post': {
                idealRatio: 1.91, // 1.91:1
                minWidth: 1200,
                maxWidth: 1200,
                ratioTolerance: 0.3,
                description: 'Landscape format (1.91:1 ratio)'
            },
            'Facebook Cover Photo': {
                idealRatio: 2.7, // 851:315
                minWidth: 851,
                maxWidth: 851,
                ratioTolerance: 0.2,
                description: 'Wide landscape format'
            },
            'Twitter/X Post': {
                idealRatio: 1.78, // 16:9
                minWidth: 1200,
                maxWidth: 1200,
                ratioTolerance: 0.3,
                description: 'Landscape format (16:9 ratio)'
            },
            'Twitter/X Header': {
                idealRatio: 3.0, // 1500:500
                minWidth: 1500,
                maxWidth: 1500,
                ratioTolerance: 0.2,
                description: 'Wide banner format'
            },
            'LinkedIn Post': {
                idealRatio: 1.78, // 16:9
                minWidth: 1200,
                maxWidth: 1200,
                ratioTolerance: 0.3,
                description: 'Professional landscape format'
            },
            'LinkedIn Cover': {
                idealRatio: 4.0, // 1584:396
                minWidth: 1584,
                maxWidth: 1584,
                ratioTolerance: 0.2,
                description: 'Professional banner format'
            },
            'YouTube Thumbnail': {
                idealRatio: 1.78, // 16:9
                minWidth: 1280,
                maxWidth: 1280,
                ratioTolerance: 0.1,
                description: 'Video thumbnail format (16:9 ratio)'
            },
            'TikTok Video': {
                idealRatio: 0.5625, // 9:16
                minWidth: 1080,
                maxWidth: 1080,
                ratioTolerance: 0.1,
                description: 'Vertical video format (9:16 ratio)'
            },
            'Pinterest Pin': {
                idealRatio: 0.67, // 2:3
                minWidth: 1000,
                maxWidth: 1000,
                ratioTolerance: 0.2,
                description: 'Vertical format (2:3 ratio)'
            },
            'Snapchat Story': {
                idealRatio: 0.5625, // 9:16
                minWidth: 1080,
                maxWidth: 1080,
                ratioTolerance: 0.1,
                description: 'Vertical story format (9:16 ratio)'
            }
        };

        // Analyze each platform
        Object.entries(platformSpecs).forEach(([platform, specs]) => {
            const ratioMatch = Math.abs(aspectRatio - specs.idealRatio) <= specs.ratioTolerance;
            const sizeMatch = width >= specs.minWidth * 0.8; // Allow 20% tolerance

            let score = 0;
            let recommendation = '';
            let details = '';

            if (ratioMatch && sizeMatch) {
                score = 100;
                recommendation = '✅ Perfect fit - Ideal dimensions and aspect ratio';
                details = `Matches ${specs.description} requirements perfectly.`;
            } else if (ratioMatch) {
                score = 75;
                recommendation = '✅ Good fit - Correct aspect ratio';
                details = `Correct ${specs.description}, but consider resizing to ${specs.minWidth}px width for optimal quality.`;
            } else if (sizeMatch) {
                score = 50;
                recommendation = '⚠️ Partial fit - Good size but needs cropping';
                details = `Good resolution but aspect ratio should be adjusted to ${specs.description}.`;
            } else {
                score = 25;
                recommendation = '❌ Poor fit - Requires significant resizing';
                details = `Needs resizing and cropping to match ${specs.description}.`;
            }

            platforms.push({
                name: platform,
                recommendation,
                details,
                score
            });

            if (score > bestScore) {
                bestScore = score;
                bestFit = platform;
            }
        });

        // Sort platforms by score
        platforms.sort((a, b) => b.score - a.score);

        // Generate overview and optimization tips
        const overview = this.generateSocialMediaOverview(aspectRatio, width, height);
        const optimizationTips = this.generateSocialMediaOptimizationTips(aspectRatio, width, height);

        return {
            platforms: platforms.slice(0, 8), // Show top 8 platforms
            bestFit: bestFit || 'Custom dimensions',
            overview,
            optimizationTips
        };
    }

    generateSocialMediaOverview(aspectRatio, width, height) {
        if (aspectRatio >= 0.9 && aspectRatio <= 1.1) {
            return 'This square-format image is ideal for Instagram feed posts and works well across most social media platforms. Square images are versatile and display consistently across different devices.';
        } else if (aspectRatio < 0.7) {
            return 'This vertical image is perfect for mobile-first platforms like Instagram Stories, TikTok, and Pinterest. Vertical content performs exceptionally well on mobile devices and story formats.';
        } else if (aspectRatio > 1.5) {
            return 'This landscape image is well-suited for Facebook posts, Twitter, LinkedIn, and YouTube thumbnails. Horizontal formats work great for desktop viewing and professional content.';
        } else {
            return 'This image has a moderate aspect ratio that can work across multiple platforms with some adjustments. Consider the specific platform requirements for optimal engagement.';
        }
    }

    generateSocialMediaOptimizationTips(aspectRatio, width, height) {
        const tips = [];

        if (width < 1080) {
            tips.push('Consider upscaling to at least 1080px width for better quality on high-resolution displays.');
        }

        if (aspectRatio >= 0.9 && aspectRatio <= 1.1) {
            tips.push('Create vertical crops (9:16) for Stories and horizontal crops (16:9) for maximum platform coverage.');
        } else if (aspectRatio < 0.7) {
            tips.push('This vertical format is perfect as-is for Stories. Consider creating a square crop for feed posts.');
        } else if (aspectRatio > 1.5) {
            tips.push('This landscape format works great for most platforms. Consider creating square and vertical versions for complete coverage.');
        }

        tips.push('Always preview your image on mobile devices as most social media consumption happens on mobile.');
        tips.push('Use high contrast and readable text if adding overlays, as images may be viewed on small screens.');
        tips.push('Consider the platform\'s compression - use high-quality originals as platforms will compress your images.');

        return tips.join(' ');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Main analysis function
    async analyzeImage(file, imageElement) {
        const startTime = Date.now();

        try {
            // Try OpenAI API first if API key is configured
            if (this.apiKey && this.apiKey !== 'YOUR_API_KEY') {
                const imageBase64 = await this.imageToBase64(file);
                const analysis = await this.analyzeWithOpenAI(imageBase64);

                return {
                    html: `
                        <div class="intro-section">
                            <h1>AI Image Analysis: ${file.name}</h1>
                        </div>
                        <div class="ai-analysis">
                            ${analysis.split('\n').map(line => `<p>${line}</p>`).join('')}
                        </div>
                    `,
                    wordCount: analysis.split(' ').length,
                    generationTime: Date.now() - startTime,
                    source: 'OpenAI Vision API'
                };
            }
        } catch (error) {
            console.warn('AI API analysis failed, using fallback:', error);
        }

        // Use fallback analysis
        const fallbackHtml = await this.fallbackImageAnalysis(file, imageElement);

        return {
            html: fallbackHtml,
            wordCount: this.countWords(fallbackHtml),
            generationTime: Date.now() - startTime,
            source: 'Technical Analysis'
        };
    }

    countWords(html) {
        const text = html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
        return text.split(' ').filter(word => word.length > 0).length;
    }
}

// Export for use in main script
window.ImageAnalyzer = ImageAnalyzer;
