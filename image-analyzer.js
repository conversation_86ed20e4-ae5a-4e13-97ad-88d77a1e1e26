// Image Analysis Module
class ImageAnalyzer {
    constructor() {
        this.apiKey = 'YOUR_API_KEY'; // Users will need to add their own API key
        this.apiEndpoint = 'https://api.openai.com/v1/chat/completions';
        this.fallbackAnalysis = true; // Enable fallback analysis when API is not available
    }

    // Convert image to base64
    async imageToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    // Analyze image using OpenAI Vision API (requires API key)
    async analyzeWithOpenAI(imageBase64) {
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                    model: "gpt-4-vision-preview",
                    messages: [
                        {
                            role: "user",
                            content: [
                                {
                                    type: "text",
                                    text: "Please analyze this image in detail. Provide comprehensive information about what you see, including objects, people, activities, setting, colors, composition, and any other relevant details. Format your response as a detailed analysis suitable for a knowledge search engine."
                                },
                                {
                                    type: "image_url",
                                    image_url: {
                                        url: imageBase64
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens: 1000
                })
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }

            const data = await response.json();
            return data.choices[0].message.content;
        } catch (error) {
            console.error('OpenAI API Error:', error);
            throw error;
        }
    }

    // Fallback analysis using image metadata and basic detection
    async fallbackImageAnalysis(file, imageElement) {
        const analysis = {
            fileName: file.name,
            fileSize: this.formatFileSize(file.size),
            fileType: file.type,
            dimensions: {
                width: imageElement.naturalWidth,
                height: imageElement.naturalHeight
            },
            aspectRatio: (imageElement.naturalWidth / imageElement.naturalHeight).toFixed(2)
        };

        // Generate comprehensive analysis
        return this.generateFallbackContent(analysis);
    }

    generateFallbackContent(analysis) {
        const { fileName, fileSize, fileType, dimensions, aspectRatio } = analysis;
        
        // Determine image category based on dimensions and aspect ratio
        let category = 'General Image';
        let orientation = 'Square';
        
        if (aspectRatio > 1.5) {
            orientation = 'Landscape';
            category = 'Landscape/Wide Image';
        } else if (aspectRatio < 0.7) {
            orientation = 'Portrait';
            category = 'Portrait/Tall Image';
        }

        // Determine quality based on dimensions
        let quality = 'Standard';
        const totalPixels = dimensions.width * dimensions.height;
        if (totalPixels > 2000000) quality = 'High Resolution';
        else if (totalPixels < 500000) quality = 'Low Resolution';

        // Generate format-specific insights
        let formatInsights = '';
        if (fileType.includes('jpeg') || fileType.includes('jpg')) {
            formatInsights = 'JPEG format is ideal for photographs with many colors and gradients. This format uses lossy compression to reduce file size while maintaining visual quality.';
        } else if (fileType.includes('png')) {
            formatInsights = 'PNG format supports transparency and uses lossless compression. This format is excellent for graphics, logos, and images requiring sharp edges.';
        } else if (fileType.includes('gif')) {
            formatInsights = 'GIF format supports animation and uses a limited color palette. This format is commonly used for simple animations and graphics.';
        } else if (fileType.includes('webp')) {
            formatInsights = 'WebP is a modern format that provides superior compression and quality compared to JPEG and PNG, supporting both lossy and lossless compression.';
        }

        return `
            <div class="intro-section">
                <h1>Image Analysis: ${fileName}</h1>
                <p>Comprehensive analysis of the uploaded image including technical specifications, format details, and visual characteristics.</p>
            </div>

            <h2>Technical Specifications</h2>
            <p><strong>File Name:</strong> ${fileName}</p>
            <p><strong>File Size:</strong> ${fileSize}</p>
            <p><strong>Format:</strong> ${fileType.toUpperCase()}</p>
            <p><strong>Dimensions:</strong> ${dimensions.width} × ${dimensions.height} pixels</p>
            <p><strong>Aspect Ratio:</strong> ${aspectRatio}:1 (${orientation})</p>
            <p><strong>Resolution Quality:</strong> ${quality}</p>
            <p><strong>Total Pixels:</strong> ${totalPixels.toLocaleString()}</p>

            <h2>Format Analysis</h2>
            <p>${formatInsights}</p>
            <p>The image format affects both file size and quality. Understanding format characteristics helps in choosing the right format for specific use cases such as web display, printing, or archival storage.</p>

            <h2>Visual Characteristics</h2>
            <p><strong>Image Category:</strong> ${category}</p>
            <p><strong>Orientation:</strong> ${orientation} orientation images are ${this.getOrientationDescription(orientation)}</p>
            <p><strong>Display Suitability:</strong> ${this.getDisplaySuitability(dimensions, aspectRatio)}</p>

            <h2>Usage Recommendations</h2>
            <p>${this.getUsageRecommendations(fileType, dimensions, aspectRatio)}</p>

            <h2>Optimization Suggestions</h2>
            <p>${this.getOptimizationSuggestions(fileSize, fileType, totalPixels)}</p>

            <div class="conclusion-section">
                <h2>Summary</h2>
                <p>This ${quality.toLowerCase()} ${orientation.toLowerCase()} image in ${fileType.toUpperCase()} format provides ${this.getQualityAssessment(totalPixels)}. The technical specifications indicate it is ${this.getSuitabilityAssessment(dimensions, fileSize)} for most digital applications.</p>
                <p><em>Note: For detailed visual content analysis including object recognition, scene description, and contextual information, please consider using an AI vision service with your API key.</em></p>
            </div>
        `;
    }

    getOrientationDescription(orientation) {
        switch (orientation) {
            case 'Landscape':
                return 'well-suited for wide scenes, panoramas, and desktop wallpapers.';
            case 'Portrait':
                return 'ideal for mobile displays, social media posts, and vertical compositions.';
            default:
                return 'versatile for various display contexts and social media platforms.';
        }
    }

    getDisplaySuitability(dimensions, aspectRatio) {
        if (dimensions.width >= 1920 && dimensions.height >= 1080) {
            return 'Excellent for high-resolution displays, printing, and professional use.';
        } else if (dimensions.width >= 1280 && dimensions.height >= 720) {
            return 'Good for web display, social media, and standard digital use.';
        } else {
            return 'Suitable for thumbnails, icons, and low-bandwidth applications.';
        }
    }

    getUsageRecommendations(fileType, dimensions, aspectRatio) {
        let recommendations = [];
        
        if (fileType.includes('jpeg')) {
            recommendations.push('Ideal for photographs and images with complex color gradients.');
        }
        if (fileType.includes('png')) {
            recommendations.push('Perfect for graphics requiring transparency or sharp edges.');
        }
        if (dimensions.width >= 1920) {
            recommendations.push('Suitable for high-quality printing and professional presentations.');
        }
        if (aspectRatio > 1.5) {
            recommendations.push('Excellent for banner images, headers, and landscape photography.');
        }
        if (aspectRatio < 0.8) {
            recommendations.push('Great for mobile-first designs and social media stories.');
        }

        return recommendations.join(' ') || 'This image format and dimensions make it versatile for various digital applications.';
    }

    getOptimizationSuggestions(fileSize, fileType, totalPixels) {
        let suggestions = [];
        
        if (fileSize > 5000000) { // 5MB
            suggestions.push('Consider compressing the image to reduce file size for web use.');
        }
        if (fileType.includes('png') && totalPixels > 1000000) {
            suggestions.push('For photographs, JPEG format might provide better compression.');
        }
        if (totalPixels > 4000000) {
            suggestions.push('Consider creating multiple sizes for responsive web design.');
        }
        if (!fileType.includes('webp')) {
            suggestions.push('WebP format could provide better compression while maintaining quality.');
        }

        return suggestions.join(' ') || 'The current format and size are well-optimized for most use cases.';
    }

    getQualityAssessment(totalPixels) {
        if (totalPixels > 8000000) return 'exceptional detail and clarity suitable for professional applications';
        if (totalPixels > 2000000) return 'good detail and quality for most digital uses';
        if (totalPixels > 500000) return 'adequate quality for web and social media';
        return 'basic quality suitable for thumbnails and low-resolution displays';
    }

    getSuitabilityAssessment(dimensions, fileSize) {
        if (dimensions.width >= 1920 && fileSize < 2000000) return 'well-optimized';
        if (dimensions.width >= 1280) return 'suitable';
        return 'appropriate';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Main analysis function
    async analyzeImage(file, imageElement) {
        const startTime = Date.now();
        
        try {
            // Try OpenAI API first if API key is configured
            if (this.apiKey && this.apiKey !== 'YOUR_API_KEY') {
                const imageBase64 = await this.imageToBase64(file);
                const analysis = await this.analyzeWithOpenAI(imageBase64);
                
                return {
                    html: `
                        <div class="intro-section">
                            <h1>AI Image Analysis: ${file.name}</h1>
                        </div>
                        <div class="ai-analysis">
                            ${analysis.split('\n').map(line => `<p>${line}</p>`).join('')}
                        </div>
                    `,
                    wordCount: analysis.split(' ').length,
                    generationTime: Date.now() - startTime,
                    source: 'OpenAI Vision API'
                };
            }
        } catch (error) {
            console.warn('AI API analysis failed, using fallback:', error);
        }

        // Use fallback analysis
        const fallbackHtml = await this.fallbackImageAnalysis(file, imageElement);
        
        return {
            html: fallbackHtml,
            wordCount: this.countWords(fallbackHtml),
            generationTime: Date.now() - startTime,
            source: 'Technical Analysis'
        };
    }

    countWords(html) {
        const text = html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
        return text.split(' ').filter(word => word.length > 0).length;
    }
}

// Export for use in main script
window.ImageAnalyzer = ImageAnalyzer;
