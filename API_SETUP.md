# Image Analysis API Setup

## Overview
The image analysis feature supports two modes:
1. **Technical Analysis** (Default) - Provides detailed technical information about images without requiring any API keys
2. **AI Vision Analysis** (Optional) - Uses OpenAI's Vision API for detailed content analysis

## Default Mode (No Setup Required)
By default, the image analyzer provides comprehensive technical analysis including:
- File specifications (size, format, dimensions)
- Image quality assessment
- Format recommendations
- Usage suggestions
- Optimization tips

This mode works immediately without any configuration.

## AI Vision Analysis Setup (Optional)

### Step 1: Get OpenAI API Key
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the API key (starts with `sk-`)

### Step 2: Configure the Application
1. Open `image-analyzer.js` file
2. Find line 4: `this.apiKey = 'YOUR_API_KEY';`
3. Replace `'YOUR_API_KEY'` with your actual OpenAI API key
4. Save the file

Example:
```javascript
this.apiKey = 'sk-your-actual-api-key-here';
```

### Step 3: Test the Setup
1. Open the website
2. Switch to "Image Analysis" mode
3. Upload an image
4. Click "Analyze Image"
5. If configured correctly, you'll get detailed AI-powered analysis

## API Costs
- OpenAI Vision API charges per image analyzed
- Current pricing: ~$0.01-0.02 per image (check OpenAI pricing for current rates)
- The fallback technical analysis is completely free

## Troubleshooting

### Common Issues:
1. **API Key Error**: Ensure your API key is correct and has sufficient credits
2. **CORS Issues**: The API calls work from localhost and most hosting environments
3. **Rate Limits**: OpenAI has rate limits; wait a moment between requests if you hit limits

### Fallback Behavior:
If the AI API fails for any reason, the system automatically falls back to technical analysis, ensuring the feature always works.

## Security Note
- Never commit your API key to version control
- Consider using environment variables for production deployments
- The current setup is suitable for personal/development use

## Alternative APIs
You can modify `image-analyzer.js` to use other vision APIs:
- Google Cloud Vision API
- Azure Computer Vision
- AWS Rekognition
- Clarifai

Simply update the `analyzeWithOpenAI` method to call your preferred service.
